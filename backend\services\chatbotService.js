const AmazonReview = require('../models/AmazonReview');
const AmazonProduct = require('../models/AmazonProduct');
const InstacartProduct = require('../models/InstacartProduct');
const InstacartOrderProduct = require('../models/InstacartOrderProduct');
const ChatbotQuery = require('../models/ChatbotQuery');
const DemandForecast = require('../models/DemandForecast');
const Product = require('../models/Product');
const Supplier = require('../models/Supplier');
const GeminiService = require('./geminiService');

class ChatbotService {
  constructor() {
    this.geminiService = new GeminiService();
    this.intentPatterns = {
      product_search: [
        /find.*product/i,
        /search.*for/i,
        /looking.*for/i,
        /show.*me.*products/i,
        /what.*products/i
      ],
      review_analysis: [
        /reviews.*for/i,
        /what.*people.*say/i,
        /customer.*reviews/i,
        /feedback.*about/i,
        /opinions.*on/i
      ],
      sentiment_analysis: [
        /sentiment.*about/i,
        /how.*people.*feel/i,
        /positive.*negative/i,
        /customer.*satisfaction/i,
        /rating.*analysis/i
      ],
      recommendation: [
        /recommend/i,
        /suggest/i,
        /similar.*products/i,
        /alternatives/i,
        /what.*should.*buy/i
      ],
      comparison: [
        /compare/i,
        /versus/i,
        /vs/i,
        /difference.*between/i,
        /which.*better/i
      ],
      forecast_demand: [
        /forecast/i,
        /predict.*demand/i,
        /future.*sales/i,
        /demand.*prediction/i,
        /sales.*forecast/i
      ],
      check_inventory: [
        /inventory/i,
        /stock.*level/i,
        /how.*many.*in.*stock/i,
        /availability/i,
        /reorder/i
      ]
    };

    this.entityPatterns = {
      product: /\b([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\b/g,
      brand: /\b(brand|made by|from)\s+([A-Z][a-z]+)\b/gi,
      category: /\b(category|type|kind)\s+([a-z]+)\b/gi,
      rating: /\b([1-5])\s*star/gi,
      sentiment: /\b(positive|negative|neutral|good|bad)\b/gi,
      price: /\$(\d+(?:\.\d{2})?)/g
    };
  }

  /**
   * Process a chatbot query
   */
  async processQuery(queryText, sessionId, userId = null) {
    const startTime = Date.now();
    
    try {
      // Create query record
      const query = new ChatbotQuery({
        query_id: `query_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        query_text: queryText,
        session_id: sessionId,
        user_id: userId,
        status: 'processing'
      });

      // Process the query
      const processedQuery = this.preprocessQuery(queryText);
      query.processed_query = processedQuery;

      // Extract entities and intent
      const entities = this.extractEntities(queryText);
      const intent = this.detectIntent(queryText);

      query.extracted_entities = entities;
      query.query_intent = this.mapIntentToQueryIntent(intent);
      query.query_type = this.getQueryType(intent);

      // Generate response based on intent
      const response = await this.generateResponse(intent, entities, processedQuery, query);
      
      // Update query with response
      query.response_text = response.text;
      query.response_data = response.data;
      query.response_type = response.type;
      query.confidence_score = response.confidence;
      query.updateProcessingTime(startTime);
      query.status = 'completed';

      await query.save();

      return {
        query_id: query.query_id,
        response: response.text,
        response_data: response.data,
        response_type: response.type,
        confidence: response.confidence,
        processing_time: query.processing_time_ms
      };

    } catch (error) {
      console.error('Error processing chatbot query:', error);
      
      // Save failed query with all required fields
      const failedQuery = new ChatbotQuery({
        query_id: `query_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        query_text: queryText,
        session_id: sessionId,
        user_id: userId,
        query_type: 'general_question', // Default query type for failed queries
        query_intent: 'get_statistics', // Default query intent for failed queries
        response_text: "I'm sorry, I encountered an error processing your request. Please try again or rephrase your question.",
        status: 'failed',
        error_message: error.message,
        processing_time_ms: Date.now() - startTime,
        confidence_score: 0,
        response_type: 'text'
      });
      
      try {
        await failedQuery.save();
      } catch (saveError) {
        console.error('Error saving failed query:', saveError);
      }

      return {
        query_id: failedQuery.query_id,
        response: "I'm sorry, I encountered an error processing your request. Please try again or rephrase your question.",
        response_type: 'text',
        confidence: 0,
        error: error.message
      };
    }
  }

  /**
   * Preprocess query text
   */
  preprocessQuery(queryText) {
    return queryText
      .toLowerCase()
      .trim()
      .replace(/[^\w\s]/g, ' ')
      .replace(/\s+/g, ' ');
  }

  /**
   * Extract entities from query text
   */
  extractEntities(queryText) {
    const entities = [];

    Object.entries(this.entityPatterns).forEach(([entityType, pattern]) => {
      const matches = queryText.matchAll(pattern);
      for (const match of matches) {
        entities.push({
          entity_type: entityType,
          entity_value: match[1] || match[0],
          confidence: 0.8
        });
      }
    });

    return entities;
  }

  /**
   * Detect intent from query text
   */
  detectIntent(queryText) {
    let bestIntent = 'general_question';
    let maxMatches = 0;

    Object.entries(this.intentPatterns).forEach(([intent, patterns]) => {
      const matches = patterns.reduce((count, pattern) => {
        return count + (pattern.test(queryText) ? 1 : 0);
      }, 0);

      if (matches > maxMatches) {
        maxMatches = matches;
        bestIntent = intent;
      }
    });

    return bestIntent;
  }

  /**
   * Get query type from intent
   */
  getQueryType(intent) {
    const intentToTypeMap = {
      product_search: 'product_search',
      review_analysis: 'review_analysis',
      sentiment_analysis: 'sentiment_analysis',
      recommendation: 'recommendation',
      comparison: 'comparison',
      forecast_demand: 'demand_forecast',
      check_inventory: 'inventory_query',
      general_question: 'general_question'
    };

    return intentToTypeMap[intent] || 'general_question';
  }

  /**
   * Map intent to query_intent enum values
   */
  mapIntentToQueryIntent(intent) {
    const intentMap = {
      product_search: 'find_product',
      review_analysis: 'get_reviews',
      sentiment_analysis: 'analyze_sentiment',
      recommendation: 'get_recommendations',
      comparison: 'compare_products',
      forecast_demand: 'forecast_demand',
      check_inventory: 'check_inventory',
      general_question: 'get_statistics'
    };

    return intentMap[intent] || 'get_statistics';
  }

  /**
   * Generate response based on intent and entities
   */
  async generateResponse(intent, entities, processedQuery, queryRecord) {
    switch (intent) {
      case 'product_search':
        return await this.handleProductSearch(entities, processedQuery, queryRecord);
      
      case 'review_analysis':
        return await this.handleReviewAnalysis(entities, processedQuery, queryRecord);
      
      case 'sentiment_analysis':
        return await this.handleSentimentAnalysis(entities, processedQuery, queryRecord);
      
      case 'recommendation':
        return await this.handleRecommendation(entities, processedQuery, queryRecord);
      
      case 'comparison':
        return await this.handleComparison(entities, processedQuery, queryRecord);
      
      case 'forecast_demand':
        return await this.handleDemandForecast(entities, processedQuery, queryRecord);
      
      case 'check_inventory':
        return await this.handleInventoryQuery(entities, processedQuery, queryRecord);
      
      default:
        return await this.handleGeneralQuestion(entities, processedQuery, queryRecord);
    }
  }

  /**
   * Handle general queries with Gemini AI integration
   */
  async handleGeneralQuery(processedQuery, queryRecord) {
    try {
      // Try to use Gemini for intelligent responses first
      if (this.geminiService.isAvailable()) {
        const userContext = await this.getUserContext(queryRecord.user_id);

        // Check if it's inventory-related and get relevant data
        const inventoryKeywords = ['inventory', 'stock', 'product', 'supplier', 'reorder', 'demand', 'forecast'];
        const isInventoryRelated = inventoryKeywords.some(keyword =>
          processedQuery.toLowerCase().includes(keyword)
        );

        if (isInventoryRelated) {
          // Get inventory data for context
          const inventoryData = await this.getInventoryDataForContext();
          const geminiResponse = await this.geminiService.generateInventoryInsights(
            queryRecord.query_text,
            inventoryData
          );

          if (geminiResponse && geminiResponse.text) {
            return {
              text: geminiResponse.text,
              data: inventoryData,
              type: 'text',
              confidence: geminiResponse.confidence || 0.8,
              source: 'gemini_inventory'
            };
          }
        } else {
          // Handle general questions with Gemini
          const geminiResponse = await this.geminiService.handleGeneralQuestion(
            queryRecord.query_text,
            userContext
          );

          if (geminiResponse && geminiResponse.text) {
            return {
              text: geminiResponse.text,
              data: null,
              type: 'text',
              confidence: geminiResponse.confidence || 0.7,
              source: 'gemini_general'
            };
          }
        }
      }

      // Fallback to original logic if Gemini is not available
      const inventoryKeywords = ['inventory', 'stock', 'product', 'supplier', 'reorder', 'demand', 'forecast'];
      const isInventoryRelated = inventoryKeywords.some(keyword =>
        processedQuery.toLowerCase().includes(keyword)
      );

      if (isInventoryRelated) {
        // Try to provide inventory-specific insights
        const insights = await this.getInventoryInsights(processedQuery);
        if (insights) {
          return insights;
        }
      }

      // For general questions, provide a helpful response with suggestions
      const suggestions = [
        "Search for products in our inventory",
        "Check stock levels for specific items",
        "Get demand forecasts for products",
        "Find supplier information",
        "Analyze product reviews and ratings"
      ];

      return {
        text: `I'm your inventory management assistant! I can help you with:\n\n• Product searches and recommendations\n• Stock level monitoring\n• Demand forecasting\n• Supplier information\n• Review analysis\n\nWhat inventory-related task can I help you with today?`,
        data: null,
        type: 'text',
        confidence: 0.7,
        suggestions: suggestions,
        source: 'fallback'
      };

    } catch (error) {
      console.error('Error in general query handling:', error);
      return {
        text: "I'm here to help with inventory management tasks. Could you please rephrase your question or ask about products, stock levels, forecasts, or suppliers?",
        data: null,
        type: 'text',
        confidence: 0.5,
        source: 'error_fallback'
      };
    }
  }

  /**
   * Get inventory-specific insights for general queries
   */
  async getInventoryInsights(query) {
    try {
      const queryLower = query.toLowerCase();

      // Low stock insights
      if (queryLower.includes('low stock') || queryLower.includes('running out')) {
        const lowStockProducts = await Product.find({ quantity: { $lt: 10 } }).limit(5);

        if (lowStockProducts.length > 0) {
          let responseText = `📊 Current Low Stock Alert:\n\n`;
          responseText += `Found ${lowStockProducts.length} products with low stock:\n\n`;

          const tableData = lowStockProducts.map((product, index) => ({
            '#': index + 1,
            'Product': product.name,
            'Current Stock': product.quantity,
            'Category': product.category,
            'Price': `$${product.price}`
          }));

          lowStockProducts.forEach((product, index) => {
            responseText += `${index + 1}. ${product.name} - Only ${product.quantity} left\n`;
          });

          return {
            text: responseText,
            data: lowStockProducts,
            table_data: tableData,
            type: 'table',
            confidence: 0.9
          };
        }
      }

      // Supplier insights
      if (queryLower.includes('supplier')) {
        const suppliers = await Supplier.find({}).limit(5);

        if (suppliers.length > 0) {
          let responseText = `📋 Supplier Information:\n\n`;

          const tableData = suppliers.map((supplier, index) => ({
            '#': index + 1,
            'Supplier Name': supplier.name,
            'Contact': supplier.contact_email,
            'Phone': supplier.contact_phone || 'N/A',
            'Lead Time': `${supplier.lead_time_days || 7} days`
          }));

          return {
            text: responseText,
            data: suppliers,
            table_data: tableData,
            type: 'table',
            confidence: 0.9
          };
        }
      }

      // Category insights
      if (queryLower.includes('category') || queryLower.includes('categories')) {
        const categories = await Product.aggregate([
          { $group: { _id: '$category', count: { $sum: 1 }, avgPrice: { $avg: '$price' } } },
          { $sort: { count: -1 } },
          { $limit: 10 }
        ]);

        if (categories.length > 0) {
          let responseText = `📊 Product Categories Overview:\n\n`;

          const tableData = categories.map((cat, index) => ({
            '#': index + 1,
            'Category': cat._id,
            'Products': cat.count,
            'Avg Price': `$${cat.avgPrice.toFixed(2)}`
          }));

          return {
            text: responseText,
            data: categories,
            table_data: tableData,
            type: 'table',
            confidence: 0.9
          };
        }
      }

      return null;
    } catch (error) {
      console.error('Error getting inventory insights:', error);
      return null;
    }
  }

  /**
   * Handle product search queries
   */
  async handleProductSearch(entities, processedQuery, queryRecord) {
    try {
      const searchTerms = entities
        .filter(e => e.entity_type === 'product')
        .map(e => e.entity_value)
        .join(' ');

      const searchQuery = searchTerms || processedQuery;

      // Search in Amazon products
      const amazonProducts = await AmazonProduct.searchProducts(searchQuery, 8);

      // Search in Instacart products with populated data
      const instacartProducts = await InstacartProduct.find({
        product_name: new RegExp(searchQuery, 'i')
      })
      .populate('aisle_id')
      .populate('department_id')
      .limit(10);

      // Search in inventory products
      const inventoryProducts = await Product.find({
        $or: [
          { name: new RegExp(searchQuery, 'i') },
          { description: new RegExp(searchQuery, 'i') },
          { category: new RegExp(searchQuery, 'i') }
        ]
      }).populate('supplier').limit(8);

      queryRecord.addDataSource('amazon_products', `searchProducts("${searchQuery}")`, amazonProducts.length);
      queryRecord.addDataSource('instacart_products', `find product_name regex`, instacartProducts.length);
      queryRecord.addDataSource('inventory_products', `find name/description/category regex`, inventoryProducts.length);

      // Create table data for better display
      let tableData = [];
      let responseText = `🔍 Found ${amazonProducts.length + instacartProducts.length + inventoryProducts.length} products matching "${searchQuery}":\n\n`;

      const responseData = {
        amazon_products: amazonProducts.map(p => ({
          id: p.product_id,
          title: p.title,
          rating: p.average_rating,
          reviews: p.total_reviews,
          sentiment: p.overall_sentiment
        })),
        instacart_products: instacartProducts.map(p => ({
          id: p.product_id,
          name: p.product_name,
          aisle: p.aisle_id?.aisle || 'Unknown',
          department: p.department_id?.department || 'Unknown'
        })),
        inventory_products: inventoryProducts.map(p => ({
          id: p._id,
          name: p.name,
          price: p.price,
          stock: p.quantity,
          category: p.category,
          supplier: p.supplier?.name || 'No supplier',
          low_stock: p.quantity < 10
        }))
      };

      // Create comprehensive table
      if (inventoryProducts.length > 0) {
        responseText += "📦 **Current Inventory:**\n";
        tableData = inventoryProducts.map((product, index) => ({
          '#': index + 1,
          'Product Name': product.name,
          'Category': product.category,
          'Price': `$${product.price}`,
          'Stock': product.quantity,
          'Supplier': product.supplier?.name || 'No supplier',
          'Status': product.quantity < 10 ? '⚠️ Low Stock' : '✅ In Stock'
        }));
      }

      if (amazonProducts.length > 0) {
        responseText += "\n⭐ **Amazon Fine Food Products:**\n";
        amazonProducts.forEach((product, index) => {
          responseText += `${index + 1}. ${product.title} - ${product.average_rating}⭐ (${product.total_reviews} reviews)\n`;
        });
        responseText += "\n";
      }

      if (instacartProducts.length > 0) {
        responseText += "🛒 **Market Products:**\n";
        const instacartTable = instacartProducts.map((product, index) => ({
          '#': index + 1,
          'Product Name': product.product_name,
          'Department': product.department_id?.department || 'Unknown',
          'Aisle': product.aisle_id?.aisle || 'Unknown'
        }));

        if (tableData.length === 0) {
          tableData = instacartTable;
        }
      }

      // Add insights
      const lowStockCount = inventoryProducts.filter(p => p.quantity < 10).length;
      if (lowStockCount > 0) {
        responseText += `\n⚠️ ${lowStockCount} inventory items have low stock and may need reordering.`;
      }

      if (responseData.amazon_products.length === 0 && responseData.instacart_products.length === 0 && responseData.inventory_products.length === 0) {
        responseText = `❌ No products found matching "${searchQuery}". Try different keywords or check spelling.`;
        return {
          text: responseText,
          data: null,
          type: 'text',
          confidence: 0.3
        };
      }

      // Try to enhance with Gemini insights if available
      if (this.geminiService.isAvailable() && (inventoryProducts.length > 0 || amazonProducts.length > 0)) {
        try {
          const geminiInsights = await this.geminiService.generateProductRecommendations(
            searchQuery,
            {
              inventory_products: responseData.inventory_products,
              amazon_products: responseData.amazon_products.slice(0, 3),
              search_query: searchQuery
            }
          );

          if (geminiInsights && geminiInsights.text) {
            responseText += `\n\n🤖 **AI Insights:**\n${geminiInsights.text}`;
          }
        } catch (error) {
          console.error('Error getting Gemini insights for product search:', error);
        }
      }

      return {
        text: responseText,
        data: responseData,
        table_data: tableData,
        type: tableData.length > 0 ? 'table' : 'structured_data',
        confidence: 0.9,
        summary: {
          total_found: amazonProducts.length + instacartProducts.length + inventoryProducts.length,
          inventory_items: inventoryProducts.length,
          low_stock_items: lowStockCount,
          market_items: instacartProducts.length,
          amazon_items: amazonProducts.length
        }
      };

    } catch (error) {
      console.error('Error in product search:', error);
      return {
        text: "❌ I encountered an error while searching for products. Please try again with different keywords.",
        data: null,
        type: 'text',
        confidence: 0.1
      };
    }
  }

  /**
   * Handle review analysis queries
   */
  async handleReviewAnalysis(entities, processedQuery, queryRecord) {
    try {
      const productEntity = entities.find(e => e.entity_type === 'product');
      
      if (!productEntity) {
        return {
          text: "Please specify which product you'd like to see reviews for.",
          data: null,
          type: 'text',
          confidence: 0.3
        };
      }

      const productName = productEntity.entity_value;
      
      // Find product
      const product = await AmazonProduct.findOne({
        title: new RegExp(productName, 'i')
      });

      if (!product) {
        return {
          text: `I couldn't find a product matching "${productName}". Please try a different search term.`,
          data: null,
          type: 'text',
          confidence: 0.2
        };
      }

      // Get recent reviews
      const reviews = await AmazonReview.getReviewsByProduct(product.product_id, 10);
      
      queryRecord.addDataSource('amazon_reviews', `getReviewsByProduct("${product.product_id}")`, reviews.length);

      const responseData = {
        product: {
          id: product.product_id,
          title: product.title,
          rating: product.average_rating,
          total_reviews: product.total_reviews
        },
        reviews: reviews.map(r => ({
          id: r.review_id,
          rating: r.score,
          summary: r.summary,
          text: r.text.substring(0, 200) + '...',
          sentiment: r.sentiment_label,
          date: r.time
        }))
      };

      let responseText = `Here are recent reviews for ${product.title}:\n\n`;
      responseText += `Overall Rating: ${product.average_rating}⭐ (${product.total_reviews} total reviews)\n\n`;
      
      reviews.slice(0, 5).forEach((review, index) => {
        responseText += `**Review ${index + 1}** (${review.score}⭐ - ${review.sentiment_label}):\n`;
        responseText += `"${review.summary}"\n`;
        responseText += `${review.text.substring(0, 150)}...\n\n`;
      });

      return {
        text: responseText,
        data: responseData,
        type: 'structured_data',
        confidence: 0.95
      };

    } catch (error) {
      console.error('Error in review analysis:', error);
      return {
        text: "I encountered an error while analyzing reviews. Please try again.",
        data: null,
        type: 'text',
        confidence: 0.1
      };
    }
  }

  /**
   * Handle sentiment analysis queries
   */
  async handleSentimentAnalysis(entities, processedQuery, queryRecord) {
    try {
      const productEntity = entities.find(e => e.entity_type === 'product');
      
      if (!productEntity) {
        return {
          text: "Please specify which product you'd like sentiment analysis for.",
          data: null,
          type: 'text',
          confidence: 0.3
        };
      }

      const productName = productEntity.entity_value;
      
      // Find product
      const product = await AmazonProduct.findOne({
        title: new RegExp(productName, 'i')
      });

      if (!product) {
        return {
          text: `I couldn't find a product matching "${productName}".`,
          data: null,
          type: 'text',
          confidence: 0.2
        };
      }

      // Get sentiment analysis
      const sentimentAnalysis = await AmazonReview.getProductSentimentAnalysis(product.product_id);
      
      queryRecord.addDataSource('amazon_reviews', `getProductSentimentAnalysis("${product.product_id}")`, sentimentAnalysis.length);

      const responseData = {
        product: {
          id: product.product_id,
          title: product.title,
          overall_sentiment: product.overall_sentiment
        },
        sentiment_breakdown: sentimentAnalysis,
        sentiment_distribution: product.sentiment_distribution
      };

      let responseText = `Sentiment Analysis for ${product.title}:\n\n`;
      responseText += `Overall Sentiment: ${product.overall_sentiment.toUpperCase()}\n\n`;
      responseText += `Sentiment Distribution:\n`;
      responseText += `• Positive: ${product.sentiment_distribution.positive} reviews\n`;
      responseText += `• Neutral: ${product.sentiment_distribution.neutral} reviews\n`;
      responseText += `• Negative: ${product.sentiment_distribution.negative} reviews\n\n`;

      if (product.strengths && product.strengths.length > 0) {
        responseText += `**Strengths:** ${product.strengths.slice(0, 5).join(', ')}\n`;
      }

      if (product.weaknesses && product.weaknesses.length > 0) {
        responseText += `**Areas for Improvement:** ${product.weaknesses.slice(0, 5).join(', ')}\n`;
      }

      return {
        text: responseText,
        data: responseData,
        type: 'chart',
        confidence: 0.9
      };

    } catch (error) {
      console.error('Error in sentiment analysis:', error);
      return {
        text: "I encountered an error while analyzing sentiment. Please try again.",
        data: null,
        type: 'text',
        confidence: 0.1
      };
    }
  }

  /**
   * Handle recommendation queries
   */
  async handleRecommendation(entities, processedQuery, queryRecord) {
    try {
      // Get top-rated products
      const topProducts = await AmazonProduct.getTopRatedProducts(5);

      queryRecord.addDataSource('amazon_products', 'getTopRatedProducts(5)', topProducts.length);

      const responseData = {
        recommendations: topProducts.map(p => ({
          id: p.product_id,
          title: p.title,
          rating: p.average_rating,
          reviews: p.total_reviews,
          sentiment: p.overall_sentiment,
          recommendation_score: p.recommendation_score
        }))
      };

      let responseText = "Here are my top product recommendations based on ratings and reviews:\n\n";

      topProducts.forEach((product, index) => {
        responseText += `${index + 1}. **${product.title}**\n`;
        responseText += `   Rating: ${product.average_rating}⭐ (${product.total_reviews} reviews)\n`;
        responseText += `   Sentiment: ${product.overall_sentiment}\n`;
        responseText += `   Recommendation Score: ${Math.round(product.recommendation_score * 100)}%\n\n`;
      });

      // Enhance with Gemini insights if available
      if (this.geminiService.isAvailable() && topProducts.length > 0) {
        try {
          const geminiRecommendations = await this.geminiService.generateProductRecommendations(
            queryRecord.query_text,
            { recommendations: responseData.recommendations }
          );

          if (geminiRecommendations && geminiRecommendations.text) {
            responseText += `\n🤖 **AI Analysis:**\n${geminiRecommendations.text}`;
          }
        } catch (error) {
          console.error('Error getting Gemini recommendations:', error);
        }
      }

      return {
        text: responseText,
        data: responseData,
        type: 'recommendation_list',
        confidence: 0.85
      };

    } catch (error) {
      console.error('Error in recommendations:', error);
      return {
        text: "I encountered an error while generating recommendations. Please try again.",
        data: null,
        type: 'text',
        confidence: 0.1
      };
    }
  }

  /**
   * Handle comparison queries
   */
  async handleComparison(entities, processedQuery, queryRecord) {
    try {
      const productEntities = entities.filter(e => e.entity_type === 'product');
      
      if (productEntities.length < 2) {
        return {
          text: "Please specify at least two products to compare.",
          data: null,
          type: 'text',
          confidence: 0.3
        };
      }

      const products = [];
      for (const entity of productEntities.slice(0, 2)) {
        const product = await AmazonProduct.findOne({
          title: new RegExp(entity.entity_value, 'i')
        });
        if (product) products.push(product);
      }

      if (products.length < 2) {
        return {
          text: "I couldn't find enough products to compare. Please check the product names.",
          data: null,
          type: 'text',
          confidence: 0.2
        };
      }

      queryRecord.addDataSource('amazon_products', 'product comparison', products.length);

      const responseData = {
        comparison: products.map(p => ({
          id: p.product_id,
          title: p.title,
          rating: p.average_rating,
          reviews: p.total_reviews,
          sentiment: p.overall_sentiment,
          strengths: p.strengths,
          weaknesses: p.weaknesses
        }))
      };

      let responseText = `Comparison between ${products[0].title} and ${products[1].title}:\n\n`;
      
      products.forEach((product, index) => {
        responseText += `**Product ${index + 1}: ${product.title}**\n`;
        responseText += `Rating: ${product.average_rating}⭐ (${product.total_reviews} reviews)\n`;
        responseText += `Sentiment: ${product.overall_sentiment}\n`;
        if (product.strengths && product.strengths.length > 0) {
          responseText += `Strengths: ${product.strengths.slice(0, 3).join(', ')}\n`;
        }
        if (product.weaknesses && product.weaknesses.length > 0) {
          responseText += `Weaknesses: ${product.weaknesses.slice(0, 3).join(', ')}\n`;
        }
        responseText += '\n';
      });

      return {
        text: responseText,
        data: responseData,
        type: 'table',
        confidence: 0.8
      };

    } catch (error) {
      console.error('Error in comparison:', error);
      return {
        text: "I encountered an error while comparing products. Please try again.",
        data: null,
        type: 'text',
        confidence: 0.1
      };
    }
  }

  /**
   * Handle demand forecast queries
   */
  async handleDemandForecast(entities, processedQuery, queryRecord) {
    try {
      const productEntity = entities.find(e => e.entity_type === 'product');
      
      if (!productEntity) {
        // Get general forecast summary
        const forecasts = await DemandForecast.find({ status: 'active' })
          .sort({ forecast_generated_at: -1 })
          .limit(5);

        queryRecord.addDataSource('demand_forecasts', 'find active forecasts', forecasts.length);

        let responseText = "Here are recent demand forecasts:\n\n";
        
        forecasts.forEach((forecast, index) => {
          const avgDemand = forecast.getAveragePredictedDemand(7);
          responseText += `${index + 1}. ${forecast.product_name}\n`;
          responseText += `   Avg Daily Demand: ${Math.round(avgDemand)} units\n`;
          responseText += `   Forecast Period: ${forecast.forecast_horizon_days} days\n\n`;
        });

        return {
          text: responseText,
          data: { forecasts },
          type: 'structured_data',
          confidence: 0.7
        };
      }

      // Find specific product forecast
      const productName = productEntity.entity_value;
      const forecast = await DemandForecast.findOne({
        product_name: new RegExp(productName, 'i'),
        status: 'active'
      });

      if (!forecast) {
        return {
          text: `I couldn't find demand forecast data for "${productName}".`,
          data: null,
          type: 'text',
          confidence: 0.2
        };
      }

      queryRecord.addDataSource('demand_forecasts', `find forecast for "${productName}"`, 1);

      const avgDemand7 = forecast.getAveragePredictedDemand(7);
      const avgDemand30 = forecast.getAveragePredictedDemand(30);

      const responseData = {
        product: forecast.product_name,
        forecast_data: forecast.forecast_data.slice(0, 30),
        avg_demand_7_days: avgDemand7,
        avg_demand_30_days: avgDemand30
      };

      let responseText = `Demand Forecast for ${forecast.product_name}:\n\n`;
      responseText += `Average Daily Demand (7 days): ${Math.round(avgDemand7)} units\n`;
      responseText += `Average Daily Demand (30 days): ${Math.round(avgDemand30)} units\n`;
      responseText += `Forecast Model: ${forecast.model_type}\n`;
      responseText += `Last Updated: ${forecast.forecast_generated_at.toLocaleDateString()}\n`;

      return {
        text: responseText,
        data: responseData,
        type: 'chart',
        confidence: 0.9
      };

    } catch (error) {
      console.error('Error in demand forecast:', error);
      return {
        text: "I encountered an error while retrieving demand forecasts. Please try again.",
        data: null,
        type: 'text',
        confidence: 0.1
      };
    }
  }

  /**
   * Handle inventory queries
   */
  async handleInventoryQuery(entities, processedQuery, queryRecord) {
    try {
      const productEntity = entities.find(e => e.entity_type === 'product');
      
      if (!productEntity) {
        // Get general inventory summary
        const products = await Product.find({})
          .sort({ quantity: 1 })
          .limit(10);

        queryRecord.addDataSource('inventory', 'find products sorted by quantity', products.length);

        let responseText = "Current inventory levels (lowest stock first):\n\n";
        
        products.forEach((product, index) => {
          responseText += `${index + 1}. ${product.name}\n`;
          responseText += `   Stock: ${product.quantity} units\n`;
          responseText += `   Min Stock: ${product.minStock} units\n`;
          responseText += `   Status: ${product.quantity <= product.minStock ? '⚠️ Low Stock' : '✅ In Stock'}\n\n`;
        });

        return {
          text: responseText,
          data: { products },
          type: 'structured_data',
          confidence: 0.8
        };
      }

      // Find specific product
      const productName = productEntity.entity_value;
      const product = await Product.findOne({
        name: new RegExp(productName, 'i')
      });

      if (!product) {
        return {
          text: `I couldn't find inventory information for "${productName}".`,
          data: null,
          type: 'text',
          confidence: 0.2
        };
      }

      queryRecord.addDataSource('inventory', `find product "${productName}"`, 1);

      const responseData = {
        product: {
          name: product.name,
          sku: product.sku,
          quantity: product.quantity,
          min_stock: product.minStock,
          max_stock: product.maxStock,
          status: product.quantity <= product.minStock ? 'low_stock' : 'in_stock'
        }
      };

      let responseText = `Inventory Status for ${product.name}:\n\n`;
      responseText += `SKU: ${product.sku}\n`;
      responseText += `Current Stock: ${product.quantity} units\n`;
      responseText += `Minimum Stock Level: ${product.minStock} units\n`;
      responseText += `Maximum Stock Level: ${product.maxStock} units\n`;
      responseText += `Status: ${product.quantity <= product.minStock ? '⚠️ Low Stock - Reorder Needed' : '✅ In Stock'}\n`;

      return {
        text: responseText,
        data: responseData,
        type: 'structured_data',
        confidence: 0.95
      };

    } catch (error) {
      console.error('Error in inventory query:', error);
      return {
        text: "I encountered an error while checking inventory. Please try again.",
        data: null,
        type: 'text',
        confidence: 0.1
      };
    }
  }

  /**
   * Handle general questions with Gemini AI integration
   */
  async handleGeneralQuestion(entities, processedQuery, queryRecord) {
    try {
      // Try to use Gemini for intelligent responses
      if (this.geminiService.isAvailable()) {
        // Get user context for better responses
        const userContext = await this.getUserContext(queryRecord.user_id);

        const geminiResponse = await this.geminiService.handleGeneralQuestion(
          queryRecord.query_text,
          userContext
        );

        if (geminiResponse && geminiResponse.text) {
          return {
            text: geminiResponse.text,
            data: null,
            type: 'text',
            confidence: geminiResponse.confidence || 0.8,
            source: 'gemini'
          };
        }
      }

      // Fallback to original responses if Gemini is not available
      const responses = [
        "I can help you with product searches, review analysis, sentiment analysis, recommendations, demand forecasting, and inventory queries. What would you like to know?",
        "I have access to Amazon Fine Food Reviews and Instacart market data. You can ask me about products, reviews, forecasts, or inventory levels.",
        "Try asking me things like: 'Find products similar to coffee', 'What are the reviews for organic honey?', 'Show me demand forecast for pasta', or 'Check inventory levels'."
      ];

      return {
        text: responses[Math.floor(Math.random() * responses.length)],
        data: null,
        type: 'text',
        confidence: 0.5,
        source: 'fallback'
      };

    } catch (error) {
      console.error('Error in general question handling:', error);

      return {
        text: "I'm here to help with inventory management tasks. You can ask me about products, stock levels, forecasts, suppliers, or market analysis. What would you like to know?",
        data: null,
        type: 'text',
        confidence: 0.4,
        source: 'error_fallback'
      };
    }
  }

  /**
   * Get user context for better AI responses
   */
  async getUserContext(userId) {
    try {
      if (!userId) return null;

      // Get recent queries for context
      const recentQueries = await ChatbotQuery.find({ user_id: userId })
        .sort({ createdAt: -1 })
        .limit(5)
        .select('query_text query_type');

      // Get inventory summary
      const inventorySummary = await this.getInventorySummary();

      return {
        recent_queries: recentQueries.map(q => q.query_text),
        query_types: recentQueries.map(q => q.query_type),
        inventory_summary: inventorySummary
      };
    } catch (error) {
      console.error('Error getting user context:', error);
      return null;
    }
  }

  /**
   * Get inventory data for AI context
   */
  async getInventoryDataForContext() {
    try {
      const [
        totalProducts,
        lowStockProducts,
        categories,
        recentActivity
      ] = await Promise.all([
        Product.countDocuments(),
        Product.find({ quantity: { $lt: 10 } }).limit(5),
        Product.aggregate([
          { $group: { _id: '$category', count: { $sum: 1 } } },
          { $sort: { count: -1 } },
          { $limit: 5 }
        ]),
        Product.find().sort({ updatedAt: -1 }).limit(3).select('name quantity category')
      ]);

      return {
        summary: {
          total_products: totalProducts,
          low_stock_items: lowStockProducts.length,
          categories: categories.length
        },
        low_stock_products: lowStockProducts.map(p => ({
          name: p.name,
          quantity: p.quantity,
          category: p.category
        })),
        top_categories: categories.map(c => ({
          category: c._id,
          count: c.count
        })),
        recent_activity: recentActivity.map(p =>
          `${p.name} (${p.quantity} units in ${p.category})`
        ).join(', ')
      };
    } catch (error) {
      console.error('Error getting inventory data for context:', error);
      return {
        summary: { total_products: 0, low_stock_items: 0, categories: 0 },
        low_stock_products: [],
        top_categories: [],
        recent_activity: 'No recent activity'
      };
    }
  }

  /**
   * Get inventory summary
   */
  async getInventorySummary() {
    try {
      const [totalProducts, lowStockCount, categories] = await Promise.all([
        Product.countDocuments(),
        Product.countDocuments({ quantity: { $lt: 10 } }),
        Product.distinct('category')
      ]);

      return {
        total_products: totalProducts,
        low_stock_items: lowStockCount,
        categories: categories.length
      };
    } catch (error) {
      console.error('Error getting inventory summary:', error);
      return { total_products: 0, low_stock_items: 0, categories: 0 };
    }
  }

  /**
   * Get session history
   */
  async getSessionHistory(sessionId, limit = 10) {
    try {
      return await ChatbotQuery.getSessionHistory(sessionId, limit);
    } catch (error) {
      console.error('Error getting session history:', error);
      return [];
    }
  }

  /**
   * Get user history
   */
  async getUserHistory(userId, limit = 20) {
    try {
      return await ChatbotQuery.getUserHistory(userId, limit);
    } catch (error) {
      console.error('Error getting user history:', error);
      return [];
    }
  }

  /**
   * Get chatbot analytics
   */
  async getAnalytics(days = 30) {
    try {
      const [queryAnalytics, popularQueries, failedQueries] = await Promise.all([
        ChatbotQuery.getQueryAnalytics(days),
        ChatbotQuery.getPopularQueries(days),
        ChatbotQuery.getFailedQueries(20)
      ]);

      return {
        query_analytics: queryAnalytics,
        popular_queries: popularQueries,
        failed_queries: failedQueries,
        analysis_period_days: days
      };
    } catch (error) {
      console.error('Error getting chatbot analytics:', error);
      throw error;
    }
  }
}

module.exports = ChatbotService;